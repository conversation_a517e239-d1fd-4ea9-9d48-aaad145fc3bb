package io.github.clive.luxomssystem.application

import io.github.clive.luxomssystem.common.enums.BaseStatus
import io.github.clive.luxomssystem.common.enums.BusinessType
import io.github.clive.luxomssystem.common.enums.DownloadTemplateType
import io.github.clive.luxomssystem.common.enums.MainOrderType
import io.github.clive.luxomssystem.common.enums.order.MainOrderImageDownloadTask
import io.github.clive.luxomssystem.common.exception.CognitionException
import io.github.clive.luxomssystem.common.exception.CognitionWebException
import io.github.clive.luxomssystem.common.exception.OmsBaseErrorCode
import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.doanload.DownloadTemplate
import io.github.clive.luxomssystem.domain.doanload.MainOrder
import io.github.clive.luxomssystem.domain.doanload.OrderImageTask
import io.github.clive.luxomssystem.domain.doanload.event.MainOrderEvent
import io.github.clive.luxomssystem.domain.sku.model.Sku
import io.github.clive.luxomssystem.domain.spu.model.Spu
import io.github.clive.luxomssystem.facade.download.dto.CreateDownloadTemplateRequest
import io.github.clive.luxomssystem.facade.download.dto.UpdateDownloadTemplateRequest
import io.github.clive.luxomssystem.facade.download.dto.toEntity
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContext
import io.github.clive.luxomssystem.infrastructure.config.auth.UserContextHolder
import io.github.clive.luxomssystem.infrastructure.repository.jpa.*
import io.github.clive.luxomssystem.infrastructure.repository.jpa.auth.BusinessRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SPURepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.spu.SkuRepository
import io.github.clive.luxomssystem.infrastructure.repository.redis.BusinessRedisRepository
import io.github.clive.luxomssystem.nextId
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.runBlocking
import org.springframework.context.ApplicationEventPublisher
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile
import java.io.IOException
import java.util.*
import java.util.concurrent.ConcurrentHashMap

@Service
class DownloadTemplateService(
    private val spuRepository: SPURepository,
    private val skuRepository: SkuRepository,
    private val businessRepository: BusinessRepository,
    private val subOrderRepository: SubOrderRepository,
    private val customerRepository: CustomerRepository,
    private val mainOrderRepository: MainOrderRepository,
    private val businessRedisRepository: BusinessRedisRepository,
    private val orderImageTaskRepository: OrderImageTaskRepository,
    private val downloadTemplateRepository: DownloadTemplateRepository,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    // 添加新的不带分页的findAll方法
    fun findAll(
        name: String?,
        type: DownloadTemplateType?,
    ): List<DownloadTemplate> = downloadTemplateRepository.findByName(name, type, BaseStatus.ENABLED, UserContextHolder.user!!.bizId)

    fun findAll(pageable: Pageable): Page<DownloadTemplate> =
        downloadTemplateRepository.findByStatusAndBizId(
            BaseStatus.ENABLED,
            UserContextHolder.user!!.bizId,
            pageable,
        )

    fun findById(id: Long): DownloadTemplate =
        downloadTemplateRepository.findById(id).orElseThrow {
            NoSuchElementException("Download template not found with id: $id")
        }

    @Transactional
    fun create(request: CreateDownloadTemplateRequest): DownloadTemplate = downloadTemplateRepository.save(request.toEntity())

    @Transactional
    fun update(
        id: Long,
        request: UpdateDownloadTemplateRequest,
    ): DownloadTemplate {
        val template = findById(id)
        return downloadTemplateRepository.save(
            template.copy(
                name = request.name,
                content = request.content,
                status = request.status ?: template.status,
                updatedAt = System.currentTimeMillis(),
            ),
        )
    }

    @Transactional
    fun deleteById(id: Long) {
        downloadTemplateRepository.deleteById(id)
    }

    fun generateExcelTemplate(id: Long): ByteArray {
        val template =
            downloadTemplateRepository.findById(id).orElseThrow {
                RuntimeException("Template not found")
            }
        downloadTemplateRepository.findById(id).orElseThrow {
            RuntimeException("Template not found")
        }
        return template.generateExcelTemplate()
    }

    data class DuplicateResult(
        val rowIndexes: List<Int>,
        var duplicateWithDatabase: Boolean,
        var duplicateWithOtherRow: Boolean,
    )

    /**
     * 检查子订单号是否存在潜在的重复可能性
     * @param subOrders 子订单列表
     * @return 重复的子订单号和对应的行号以及重复的原因
     */
    fun checkSubOrderDuplicate(subOrders: Collection<Pair<SubOrder, Int>>): Map<String, DuplicateResult> {
        // 来自文件内的冲突
        val duplicateResult = mutableMapOf<String, DuplicateResult>()

        val orderNoMapIndexes = mutableMapOf<String, MutableList<Int>>()
        subOrders.forEach { (subOrder, index) ->
            subOrder.orderNo?.let {
                val orderNoList = orderNoMapIndexes.computeIfAbsent(it) { mutableListOf() }
                orderNoList.add(index)
                if (orderNoList.size == 2) {
                    // 第一次冲突的时候写入冲突结果
                    duplicateResult[it] = DuplicateResult(orderNoList, duplicateWithDatabase = false, duplicateWithOtherRow = true)
                }
            }
        }

        val duplicateFromDb =
            runBlocking {
                subOrders
                    .map { (subOrder, index) ->
                        async(Dispatchers.IO) {
                            val exist =
                                subOrder.orderNo?.let {
                                    subOrderRepository.existsByOrderNoIs(it)
                                } ?: false
                            Triple(exist, subOrder, index)
                        }
                    }.awaitAll()
            }
        duplicateFromDb.forEach { (exist, subOrder, index) ->
            // 如果在数据库中存在,那么往冲突结果写入冲突或更新现有冲突结果
            if (exist) {
                // 在数据库中能找到冲突的,这里OrderNo一定不为空
                val result =
                    duplicateResult.computeIfAbsent(subOrder.orderNo!!) {
                        DuplicateResult(listOf(index), duplicateWithDatabase = true, duplicateWithOtherRow = false)
                    }
                // 之前可能是行内冲突,不需要新插入,但需要将行内冲突的更新也有数据库冲突结果
                result.duplicateWithDatabase = true
            }
        }

        return duplicateResult
    }

    /**
     * 解析上传的Excel文件
     * @param templateId 模板ID
     * @param file 上传的Excel文件
     * @param attachmentUrls 附件URL列表
     * @return 解析结果
     */
    @Transactional
    fun parseExcelFile(
        templateId: Long,
        file: MultipartFile,
        action: String,
        selectedUser: Long?,
        namingType: String,
        urlSplitToken: String,
        needDownloadImage: Boolean,
        attachmentUrls: List<String> = emptyList(),
    ): ExcelParseResult {
        val template =
            downloadTemplateRepository.findById(templateId).orElseThrow {
                IllegalArgumentException("模板不存在: $templateId")
            }

        try {
            val excelContent = file.bytes
            val parsedData = template.parseExcelContent(excelContent)

            // 收集所有验证结果和任务
            val allTasks = mutableListOf<OrderImageTask>()
            val subOrders: MutableList<SubOrder> = mutableListOf()
            val subOrdersIndexed = mutableListOf<Pair<SubOrder, Int>>()
            val validationResults =
                parsedData
                    .mapIndexed { index, row ->
                        val validationResult =
                            if (action == "compress") {
                                template.validateCompressTaskRow(row, urlSplitToken)
                            } else {
                                template.validateOrderTaskRow(row, urlSplitToken)
                            }

                        if (validationResult.isValid) {
                            allTasks.addAll(validationResult.tasks)
                            validationResult.subOrders?.let {
                                subOrders.add(it)
                                subOrdersIndexed.add(it to index + 2)
                            }
                            null
                        } else {
                            RowValidationError(
                                rowIndex = index + 2,
                                errors = validationResult.errors,
                            )
                        }
                    }.filterNotNull()
                    .toMutableList()

            val duplicateOrders = checkSubOrderDuplicate(subOrdersIndexed)
            duplicateOrders.forEach { (orderNo, duplicateResult) ->
                val errorMessage =
                    when {
                        duplicateResult.duplicateWithDatabase && duplicateResult.duplicateWithOtherRow ->
                            "订单号 $orderNo 重复, 与现有订单号重复且与文件内其他行订单号重复"

                        duplicateResult.duplicateWithDatabase -> "订单号 $orderNo 重复, 与现有订单号重复"
                        duplicateResult.duplicateWithOtherRow -> "订单号 $orderNo 重复, 与文件内其他行订单号重复"
                        else -> {
                            // 真的会走到这里吗¿
                            log.warn { "走到这里代表程序出问题了,没有冲突的结果不应该出现在返回值里" }
                            return@forEach
                        }
                    }
                duplicateResult.rowIndexes.forEach {
                    // 找到已有的错误行,没有的话尝试插入到对应的行中
                    val indexPoint = validationResults.binarySearchBy(it) { it.rowIndex }
                    if (indexPoint >= 0) {
                        val existingErrorResult = validationResults[indexPoint]
                        validationResults[indexPoint] = existingErrorResult.copy(errors = existingErrorResult.errors + errorMessage)
                    } else {
                        validationResults.add(-indexPoint - 1, RowValidationError(it, listOf(errorMessage)))
                    }
                }
            }

            // 如果没有错误，保存所有任务
            if (validationResults.isEmpty()) {
                val customerNameCache = ConcurrentHashMap<Long, String?>()
                val user = UserContextHolder.user!!
                val mainOrder =
                    mainOrderRepository.saveAndFlush(
                        MainOrder(
                            id = nextId(),
                            bizId = user.bizId,
                            fileName = file.originalFilename ?: "无名.xlsx",
                        ).apply {
                            customerId = selectedUser ?: 0
                            customerName = selectedUser?.let {
                                memoFindCustomerName(it, customerNameCache) ?: ""
                            } ?: ""
                            createdByName = user.name
                            updatedByName = user.name
                            type = if (action == "compress") MainOrderType.IMAGE else MainOrderType.ORDER
                            this.attachmentUrls = attachmentUrls
                        },
                    )
                if (action == "order") {
                    //filename must contain customer name
                    if (mainOrder.customerName !in mainOrder.fileName) {
                        throw CognitionWebException(OmsBaseErrorCode.ORDER_NO_REPEAT)
                    }
                }

                val excelParseResult =
                    processSubOrdersConcurrently(
                        subOrders,
                        mainOrder,
                        selectedUser,
                        customerNameCache,
                        user,
                    )
                if (!excelParseResult.success) {
                    return excelParseResult
                }
                subOrderRepository.saveAll(subOrders)
                val repeatedOrders = orderImageTaskRepository.findByOrderNoIn(allTasks.map { it.orderNo }, user.bizId)
                if (repeatedOrders.isNotEmpty()) {
                    throw CognitionWebException(OmsBaseErrorCode.ORDER_NO_REPEAT, repeatedOrders.joinToString(","))
                }
                allTasks.forEach { task ->
                    task.orderId = mainOrder.id // 设置关联的订单ID
                    task.namingType = namingType
                }
                mainOrder.downloadTask = MainOrderImageDownloadTask(allTasks.size)
                if (action == "compress" || (action == "order" && needDownloadImage)) {
                    orderImageTaskRepository.saveAll(allTasks)
                } else {
                    log.info { "不需要下载图片，不保存任务" }
                }
                mainOrderRepository.save(mainOrder)
                if (mainOrder.type == MainOrderType.ORDER) {
                    applicationEventPublisher.publishEvent(MainOrderEvent.MainOrderCreatedEvent(mainOrder.id))
                }
                // 测试商户限制任务数量
                rateLimit(allTasks.size.toLong())
            }

            return ExcelParseResult(
                success = validationResults.isEmpty(),
                totalRows = parsedData.size,
                errors = validationResults,
                data = if (validationResults.isEmpty()) parsedData else emptyList(),
            )
        } catch (e: IOException) {
            throw IllegalArgumentException("无法读取Excel文件: ${e.message}")
        }
    }

    @Transactional
    fun processSubOrdersConcurrently(
        subOrders: List<SubOrder>,
        mainOrder: MainOrder,
        selectedUser: Long?,
        customerNameCache: ConcurrentHashMap<Long, String?>,
        user: UserContext,
    ): ExcelParseResult {
        val errors = Collections.synchronizedList(mutableListOf<RowValidationError>())
        val spuCache = ConcurrentHashMap<String, Spu>()
        val skuCache = ConcurrentHashMap<String, Sku>()

        runBlocking {
            subOrders
                .mapIndexed { idx, subOrder ->
                    async(Dispatchers.IO) {
                        try {
                            subOrder.apply {
                                parentId = mainOrder.id
                                fileName = mainOrder.fileName
                                customerId = selectedUser!!
                                customerName = memoFindCustomerName(selectedUser, customerNameCache) ?: "未知客户名字"
                                createdByName = user.name
                                updatedByName = user.name

                                // 缓存 spu
                                val spuCode = product.spu!!
                                val spu =
                                    spuCache.computeIfAbsent(spuCode) {
                                        spuRepository.findBySpuCode(spuCode)
                                            ?: throw CognitionException("spu不存在: $spuCode")
                                    }

                                // 缓存 sku
                                val skuCode = product.determineSkuCode()
                                val sku =
                                    skuCode?.let {
                                        // 这里强转一下,computeIfAbsent的mapping返回可以为空,但是签名不让返回为空
                                        // ConcurrentHashMap.computeIfAbsent的mapping返回为空的时候会放弃插入
                                        @Suppress("UNCHECKED_CAST")
                                        (skuCache as MutableMap<String, Sku?>).computeIfAbsent(skuCode) {
                                            skuRepository.findBySkuCode(skuCode)
                                        }
                                    }

                                product.update(sku, spu)
                            }
                        } catch (e: Exception) {
                            // 收集错误，idx+1 可作为excel行号
                            errors.add(RowValidationError(idx + 2, listOf(e.message ?: "未知错误")))
                        }
                    }
                }.awaitAll()
        }

        return ExcelParseResult(
            success = errors.isEmpty(),
            totalRows = subOrders.size,
            errors = errors,
        )
    }

    @Transactional
    fun star(id: Long): DownloadTemplate {
        val template = findById(id)
        downloadTemplateRepository
            .findByTypeAndBizId(template.type, UserContextHolder.user!!.bizId)
            .map {
                it.onDefault = it.id == id
                it
            }.also {
                downloadTemplateRepository.saveAll(it)
            }
        return template
    }

    fun memoFindCustomerName(
        id: Long,
        customerNameCache: ConcurrentHashMap<Long, String?>,
    ): String? =
        customerNameCache.computeIfAbsent(id) {
            // 如果缓存中不存在，则从数据库获取
            customerRepository.findNameById(id)
        }

    fun rateLimit(newTaskCount: Long) {
        val business =
            businessRepository.findByIdOrNull(UserContextHolder.user!!.bizId)
                ?: throw IllegalArgumentException("商户不存在")
        if (business.type == BusinessType.TRAIL) {
            businessRedisRepository.countTask(business.id, newTaskCount)
        }
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}

data class ExcelParseResult(
    val success: Boolean,
    val totalRows: Int,
    val errors: List<RowValidationError> = emptyList(),
    val data: List<Map<String, String>> = emptyList(),
)

data class RowValidationError(
    val rowIndex: Int,
    val errors: List<String>,
)
